# Critical Fix: Tool Parameter Passing & Knowledge Graph Type Handling

**Date**: 2025-09-30  
**Status**: ✅ **FIXED**  
**Priority**: CRITICAL

---

## 🚨 Issues Discovered During Testing

After applying the initial fixes for `toolset.run_tool()`, two NEW critical issues were discovered during actual workflow execution:

### Issue 1: Tool Parameter Validation Errors ❌

**Error Messages**:
```
1 validation error for ListDirInput
directory
  Field required [type=missing, input_value={'path': '.'}, input_type=dict]

1 validation error for FindFilesInput
name_pattern
  Field required [type=missing, input_value={'pattern': 'package.json', 'max_results': 10}, input_type=dict]

3 validation errors for RefSearchInput
id
  Field required [type=missing, input_value={'query': 'software', 'max_results': 10}, input_type=dict]
search
  Field required [type=missing, input_value={'query': 'software', 'max_results': 10}, input_type=dict]
search_type
  Field required [type=missing, input_value={'query': 'software', 'max_results': 10}, input_type=dict]
```

**Root Cause**:
The `_execute_tool()` method was passing kwargs as a dict to `ainvoke()`, but the specialist agents were calling tools with INCORRECT parameter names that don't match the tool's input schema.

**Example**:
```python
# WRONG - Code was calling with wrong parameter names:
await self._execute_tool("list_dir", path=".")  # ❌ 'path' is wrong
await self._execute_tool("find_files", pattern="*.json")  # ❌ 'pattern' is wrong
await self._execute_tool("gitlab_blob_search", query="test")  # ❌ 'query' is wrong

# CORRECT - Should use actual schema field names:
await self._execute_tool("list_dir", directory=".")  # ✅ 'directory' is correct
await self._execute_tool("find_files", name_pattern="*.json")  # ✅ 'name_pattern' is correct
await self._execute_tool("gitlab_blob_search", id="123", search="test", search_type="projects")  # ✅ Correct fields
```

**Tool Input Schemas**:
```python
# From filesystem.py
class ListDirInput(BaseModel):
    directory: str  # NOT 'path'

class FindFilesInput(BaseModel):
    name_pattern: str  # NOT 'pattern'

# From search.py
class RefSearchInput(BaseSearchInput):  # Used by gitlab_blob_search
    id: str  # Project/group ID
    search: str  # Search term
    search_type: Literal["projects", "groups"]  # Search scope
    ref: Optional[str] = None  # Branch/tag name
```

---

### Issue 2: Knowledge Graph Type Error ❌

**Error Message**:
```
AttributeError: 'dict' object has no attribute 'nodes'
  File "enhanced_state.py", line 313, in calculate_quality_metrics
    total_nodes = len(kg.nodes)
                      ^^^^^^^^
AttributeError: 'dict' object has no attribute 'nodes'
```

**Root Cause**:
The `knowledge_graph` in the state is stored as a dict (after serialization), but the `calculate_quality_metrics()` method expects a `KnowledgeGraph` Pydantic model instance with `.nodes` and `.edges` attributes.

**Context**:
LangGraph serializes state between nodes, converting Pydantic models to dicts. When the state is passed to the next node, the `knowledge_graph` is a dict, not a `KnowledgeGraph` object.

---

## ✅ Solutions Implemented

### Fix 1: Updated `_execute_tool()` Method

**File**: `gitlab-ai-gateway/********************/agents/context_2_0/base_specialist_agent.py`

**Changes**:
```python
async def _execute_tool(self, tool_name: str, **kwargs) -> Any:
    """Execute a tool from the toolset."""
    try:
        if tool_name not in self.toolset:
            return {"success": False, "error": f"Tool '{tool_name}' not found", "content": None}

        tool = self.toolset[tool_name]

        # LangChain tools expect a dict input, not **kwargs
        # The tool's args_schema defines the expected fields
        tool_input = kwargs if kwargs else {}

        # Execute the tool using LangChain's invoke method
        # Note: ainvoke/invoke expect a dict, not **kwargs
        if hasattr(tool, 'ainvoke'):
            result = await tool.ainvoke(tool_input)  # ✅ Pass dict directly
        elif hasattr(tool, 'invoke'):
            result = tool.invoke(tool_input)  # ✅ Pass dict directly
        elif hasattr(tool, 'arun'):
            result = await tool.arun(**kwargs)  # arun accepts **kwargs
        elif hasattr(tool, 'run'):
            result = tool.run(**kwargs)  # run accepts **kwargs
        else:
            return {"success": False, "error": "Tool has no executable method", "content": None}

        return {"success": True, "content": result, "tool_name": tool_name}

    except Exception as e:
        return {"success": False, "error": f"Tool execution failed: {str(e)}", "content": None}
```

**Key Change**: Pass `tool_input` dict directly to `ainvoke()`/`invoke()` instead of unpacking as `**kwargs`.

---

### Fix 2: Added Knowledge Graph Type Handling

**File**: `gitlab-ai-gateway/********************/agents/context_2_0/enhanced_state.py`

**Changes**:
```python
@staticmethod
def calculate_quality_metrics(state: Context2State) -> ContextQualityMetrics:
    """Calculate quality metrics based on current state."""
    kg_data = state["knowledge_graph"]
    coordination = state["agent_coordination"]
    
    # Handle both dict and KnowledgeGraph instances
    if isinstance(kg_data, dict):
        # If it's a dict, create a KnowledgeGraph instance from it
        kg = KnowledgeGraph(**kg_data) if kg_data else KnowledgeGraph()
    else:
        kg = kg_data
    
    # Calculate basic metrics
    total_nodes = len(kg.nodes)  # ✅ Now works with both dict and object
    total_edges = len(kg.edges)
    # ... rest of the method
```

**Key Change**: Added type checking to handle both dict (serialized) and KnowledgeGraph (object) instances.

---

## 🔍 Why These Issues Occurred

### Issue 1: Tool Parameter Mismatch
1. The specialist agents were written with ASSUMED parameter names (like `path`, `pattern`, `query`)
2. These don't match the ACTUAL tool input schemas (like `directory`, `name_pattern`, `id`/`search`/`search_type`)
3. The `_execute_tool()` method was passing kwargs correctly, but the CALLING CODE used wrong names

### Issue 2: Knowledge Graph Serialization
1. LangGraph serializes state between nodes for checkpointing
2. Pydantic models are converted to dicts during serialization
3. The code didn't handle the deserialized dict format

---

## 📝 Important Notes

### Tool Parameter Names Reference

**Filesystem Tools**:
- `list_dir`: `directory` (NOT `path`)
- `find_files`: `name_pattern` (NOT `pattern`)
- `read_file`: `file_path`
- `read_files`: `file_paths` (list)
- `grep`: Check actual schema

**Search Tools**:
- `gitlab_blob_search`: `id`, `search`, `search_type`, `ref` (optional)
- `gitlab_issue_search`: `id`, `search`, `search_type`, + issue-specific fields
- `gitlab_merge_request_search`: `id`, `search`, `search_type`, + MR-specific fields

**GitLab Tools**:
- `get_project`: No parameters (uses context)
- `list_issues`: `state`, `sort`, `per_page`
- `get_issue`: `issue_id`
- `list_issue_notes`: `issue_id`
- `get_merge_request`: `merge_request_id`

**Git Tools**:
- `list_commits`: `per_page`, `since`
- `get_commit`: `commit_sha`
- `get_commit_diff`: `commit_sha`
- `run_read_only_git_command`: `command`

---

## 🚨 CRITICAL: All Tool Calls Need Review

**The specialist agents are calling tools with WRONG parameter names!**

This means we need to:
1. ✅ Fix `_execute_tool()` to pass dict correctly (DONE)
2. ✅ Fix knowledge graph type handling (DONE)
3. ❌ **FIX ALL TOOL CALLS** in specialist agents to use correct parameter names

### Files That Need Parameter Name Fixes:
1. `repository_explorer.py` - All tool calls
2. `code_navigator.py` - All tool calls
3. `gitlab_ecosystem.py` - All tool calls
4. `git_history.py` - All tool calls

**This is a SEPARATE issue from the `toolset.run_tool()` problem!**

---

## 🎯 Next Steps

### Immediate (CRITICAL):
1. ✅ Fixed `_execute_tool()` method
2. ✅ Fixed knowledge graph type handling
3. ❌ **MUST FIX**: Review and correct ALL tool call parameter names in specialist agents
4. ❌ **MUST TEST**: Verify tools execute successfully with correct parameters

### Testing:
1. Test each tool individually with correct parameters
2. Verify LangSmith traces show successful tool execution
3. Check that no validation errors occur
4. Verify knowledge graph is properly handled

---

## 📊 Status Summary

| Issue | Status | Priority |
|-------|--------|----------|
| `_execute_tool()` dict passing | ✅ FIXED | CRITICAL |
| Knowledge graph type handling | ✅ FIXED | CRITICAL |
| Tool parameter names in agents | ❌ **NEEDS FIX** | **CRITICAL** |
| Integration testing | ⏭️ PENDING | HIGH |

---

## 🔗 Related Documents

- `CONTEXT_2_0_FIXES_COMPLETE_SUMMARY.md` - Original toolset.run_tool() fixes
- `README_CONTEXT_2_0_FIXES.md` - Overview of all fixes
- `TESTING_GUIDE_context_2_0.md` - Testing instructions

---

**IMPORTANT**: The tool parameter name issue is SEPARATE from the original `toolset.run_tool()` issue. Both need to be fixed for the system to work correctly.

