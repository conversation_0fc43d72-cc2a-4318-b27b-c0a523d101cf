#!/usr/bin/env python3
"""
Test script to trigger a workflow and see what CategoryEnum is being used.
This will help us debug why we're still seeing 'software_development' instead of 'software_development_2_0'.
"""

import sys
import os
sys.path.append('.')

def test_category_enum_function():
    """Test the string_to_category_enum function directly."""
    try:
        from duo_workflow_service.server import string_to_category_enum
        from lib.internal_events.event_enum import CategoryEnum
        
        print("🧪 Testing string_to_category_enum function:")
        print(f"   Available CategoryEnum values: {[e.value for e in CategoryEnum]}")
        print()
        
        # Test cases
        test_cases = [
            (None, "None (Flow mode default)"),
            ("software_development", "Explicit software_development"),
            ("software_development_2_0", "Explicit software_development_2_0"),
            ("unknown_workflow", "Unknown workflow"),
        ]
        
        for input_val, description in test_cases:
            try:
                result = string_to_category_enum(input_val)
                print(f"   {description:30} -> {result.value}")
            except Exception as e:
                print(f"   {description:30} -> ERROR: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing string_to_category_enum: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_registry():
    """Test the workflow registry to see what it returns."""
    try:
        from duo_workflow_service.workflows.registry import resolve_workflow_class
        
        print("\n🧪 Testing workflow registry:")
        
        # Test cases
        test_cases = [
            (None, "None (Flow mode default)"),
            ("software_development", "Explicit software_development"),
            ("software_development_2_0", "Explicit software_development_2_0"),
        ]
        
        for input_val, description in test_cases:
            try:
                result = resolve_workflow_class(input_val)
                print(f"   {description:30} -> {result.__module__}.{result.__name__}")
            except Exception as e:
                print(f"   {description:30} -> ERROR: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow registry: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_server_logic():
    """Simulate what happens in the server when a workflow is triggered."""
    try:
        from duo_workflow_service.server import string_to_category_enum
        from duo_workflow_service.workflows.registry import resolve_workflow_class
        
        print("\n🧪 Simulating server logic for Flow mode (workflow_definition=None):")
        
        # This is what happens in server.py when Flow mode is used
        workflow_definition = None  # This is what Flow mode sends
        
        print(f"   1. workflow_definition = {workflow_definition}")
        
        # Step 1: Determine workflow_type (CategoryEnum)
        workflow_type = string_to_category_enum(workflow_definition)
        print(f"   2. string_to_category_enum({workflow_definition}) = {workflow_type.value}")
        
        # Step 2: Resolve workflow class
        workflow_class = resolve_workflow_class(workflow_definition)
        print(f"   3. resolve_workflow_class({workflow_definition}) = {workflow_class.__module__}.{workflow_class.__name__}")
        
        # Step 3: What gets passed to workflow constructor
        print(f"   4. Workflow constructor gets: workflow_type={workflow_type.value}")
        
        # Step 4: What appears in LangSmith traces
        print(f"   5. LangSmith traces will show: workflow_type={workflow_type.value}")
        
        print(f"\n✅ Expected result: workflow_type should be 'software_development_2_0'")
        print(f"✅ Actual result: workflow_type is '{workflow_type.value}'")
        
        if workflow_type.value == "software_development_2_0":
            print("🎉 SUCCESS: The fix is working correctly!")
        else:
            print("❌ PROBLEM: Still showing wrong workflow_type")
        
        return workflow_type.value == "software_development_2_0"
        
    except Exception as e:
        print(f"❌ Error simulating server logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("🔍 Debugging workflow_type issue...\n")
    
    tests = [
        ("CategoryEnum function", test_category_enum_function),
        ("Workflow registry", test_workflow_registry),
        ("Server logic simulation", simulate_server_logic),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"=" * 60)
        print(f"Testing {test_name}:")
        print("=" * 60)
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed! The fix should be working.")
    else:
        print("❌ Some tests failed. The issue may still exist.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
