#!/usr/bin/env python3
"""
Test script to verify that the new software_development_2_0 workflow is being used by default.
"""

import sys
import os

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'gitlab-ai-gateway'))

try:
    from ********************.workflows.registry import resolve_workflow_class
    
    print("🧪 Testing workflow selection...")
    
    # Test 1: Default workflow (no workflow_definition provided)
    print("\n1. Testing default workflow selection (no workflow_definition):")
    default_workflow = resolve_workflow_class(None)
    print(f"   Default workflow: {default_workflow}")
    print(f"   Module: {default_workflow.__module__}")
    
    # Test 2: Explicit software_development workflow
    print("\n2. Testing explicit 'software_development' workflow:")
    sd_workflow = resolve_workflow_class("software_development")
    print(f"   Software development workflow: {sd_workflow}")
    print(f"   Module: {sd_workflow.__module__}")
    
    # Test 3: Check if software_development_2_0 is available
    print("\n3. Testing 'software_development_2_0' workflow:")
    try:
        sd2_workflow = resolve_workflow_class("software_development_2_0")
        print(f"   Software development 2.0 workflow: {sd2_workflow}")
        print(f"   Module: {sd2_workflow.__module__}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 4: Verify the default is Context 2.0
    print("\n4. Verification:")
    if "software_development_2_0" in str(default_workflow.__module__):
        print("   ✅ SUCCESS: Default workflow is now software_development_2_0 (Context 2.0)")
        print("   🎉 Flow mode will now use the new Context 2.0 multi-agent workflow!")
    else:
        print("   ❌ FAILURE: Default workflow is still the old software_development")
        print(f"   Expected: software_development_2_0, Got: {default_workflow.__module__}")
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the GDK root directory")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
