#!/usr/bin/env python3
"""
Test Context 2.0 DAP Integration.

This script tests the Context 2.0 multi-agent system integration with the actual
GitLab Duo Agent Platform to ensure everything works in the real environment.
"""

import os
import sys
import json
import requests
import subprocess
from typing import Dict, Any, Optional


class Context2DAPTester:
    """Test Context 2.0 integration with GitLab DAP."""
    
    def __init__(self, base_url: str = "http://localhost:3000", api_token: Optional[str] = None):
        self.base_url = base_url
        self.api_token = api_token
        self.headers = {"PRIVATE-TOKEN": api_token} if api_token else {}
        self.project_id = None
        
    def test_gdk_services(self) -> bool:
        """Test that required GDK services are running."""
        print("🔍 Testing GDK Services...")
        
        try:
            # Check GDK status
            result = subprocess.run(["gdk", "status"], capture_output=True, text=True)
            status_output = result.stdout
            
            # Check for required services
            required_services = ["rails", "duo-workflow-service"]
            running_services = []
            
            for service in required_services:
                if f"{service}" in status_output and "up" in status_output:
                    running_services.append(service)
                    print(f"  ✅ {service}: running")
                else:
                    print(f"  ❌ {service}: not running")
            
            if len(running_services) == len(required_services):
                print("  ✅ All required services are running")
                return True
            else:
                print(f"  ❌ Only {len(running_services)}/{len(required_services)} services running")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking GDK services: {e}")
            return False
    
    def test_gitlab_connection(self) -> bool:
        """Test connection to local GitLab instance."""
        print("🌐 Testing GitLab Connection...")
        
        try:
            response = requests.get(f"{self.base_url}/api/v4/version", headers=self.headers, timeout=10)
            
            if response.status_code == 200:
                version_info = response.json()
                print(f"  ✅ GitLab {version_info.get('version', 'unknown')} is accessible")
                return True
            else:
                print(f"  ❌ GitLab not accessible: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Error connecting to GitLab: {e}")
            return False
    
    def find_duo_test_project(self) -> Optional[int]:
        """Find the GitLab Duo test project."""
        print("📁 Finding GitLab Duo Test Project...")
        
        try:
            # Search for gitlab-duo/test project
            response = requests.get(
                f"{self.base_url}/api/v4/projects",
                headers=self.headers,
                params={"search": "test", "owned": True},
                timeout=10
            )
            
            if response.status_code == 200:
                projects = response.json()
                
                for project in projects:
                    if project.get("path_with_namespace") == "gitlab-duo/test":
                        self.project_id = project["id"]
                        print(f"  ✅ Found project: {project['name']} (ID: {self.project_id})")
                        return self.project_id
                
                print("  ⚠️  gitlab-duo/test project not found")
                print("     Run: cd gitlab && GITLAB_SIMULATE_SAAS=1 bundle exec rake 'gitlab:duo:setup'")
                return None
            else:
                print(f"  ❌ Error searching projects: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"  ❌ Error finding project: {e}")
            return None
    
    def test_********************(self) -> bool:
        """Test Duo Workflow Service accessibility."""
        print("🤖 Testing Duo Workflow Service...")
        
        try:
            # Try to connect to duo-workflow-service port (usually 50052)
            # This is a gRPC service, so we can't use HTTP, but we can check if it's listening
            result = subprocess.run(
                ["netstat", "-an"], 
                capture_output=True, 
                text=True
            )
            
            if "50052" in result.stdout:
                print("  ✅ Duo Workflow Service is listening on port 50052")
                return True
            else:
                print("  ❌ Duo Workflow Service not listening on expected port")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking Duo Workflow Service: {e}")
            return False
    
    def test_context_2_files(self) -> bool:
        """Test that Context 2.0 files are properly integrated."""
        print("📂 Testing Context 2.0 File Integration...")
        
        context_2_dir = "gitlab-ai-gateway/********************/agents/context_2_0"
        
        required_files = [
            "context_2_workflow.py",
            "tool_distribution_system.py", 
            "tool_distribution_integration.py",
            "base_specialist_agent.py",
            "orchestrator.py",
            "repository_explorer.py",
            "code_navigator.py",
            "gitlab_ecosystem.py",
            "git_history.py",
            "context_synthesizer.py"
        ]
        
        missing_files = []
        
        for file in required_files:
            file_path = os.path.join(context_2_dir, file)
            if os.path.exists(file_path):
                print(f"  ✅ {file}")
            else:
                print(f"  ❌ {file} - missing")
                missing_files.append(file)
        
        if not missing_files:
            print("  ✅ All Context 2.0 files present")
            return True
        else:
            print(f"  ❌ Missing {len(missing_files)} files")
            return False
    
    def test_workflow_integration(self) -> bool:
        """Test that Context 2.0 is integrated into software_development_2.0 workflow."""
        print("🔗 Testing Workflow Integration...")
        
        workflow_file = "gitlab-ai-gateway/********************/workflows/software_development_2.0/workflow.py"
        
        try:
            with open(workflow_file, 'r') as f:
                content = f.read()
            
            # Check for Context 2.0 integration markers
            integration_markers = [
                "from ********************.agents.context_2_0.context_2_workflow import Context2Workflow",
                "Context2Workflow(",
                "_create_traced_context_2_run",
                "context_2_result"
            ]
            
            found_markers = []
            for marker in integration_markers:
                if marker in content:
                    found_markers.append(marker)
                    print(f"  ✅ Found: {marker}")
                else:
                    print(f"  ❌ Missing: {marker}")
            
            if len(found_markers) == len(integration_markers):
                print("  ✅ Context 2.0 fully integrated into workflow")
                return True
            else:
                print(f"  ❌ Integration incomplete: {len(found_markers)}/{len(integration_markers)} markers found")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking workflow integration: {e}")
            return False
    
    def test_api_workflow_trigger(self) -> bool:
        """Test triggering a workflow via API (if token provided)."""
        if not self.api_token or not self.project_id:
            print("🔑 Skipping API test (no token or project ID)")
            return True
        
        print("🧪 Testing API Workflow Trigger...")
        
        try:
            # Try to trigger a simple workflow
            workflow_data = {
                "goal": "Test Context 2.0 multi-agent system integration",
                "workflow_type": "software_development_2.0"
            }
            
            response = requests.post(
                f"{self.base_url}/api/v4/projects/{self.project_id}/ai/duo_workflows/workflows",
                headers={**self.headers, "Content-Type": "application/json"},
                json=workflow_data,
                timeout=30
            )
            
            if response.status_code in [200, 201, 202]:
                print("  ✅ Workflow API accessible")
                result = response.json()
                if "id" in result:
                    print(f"  ✅ Workflow triggered: ID {result['id']}")
                return True
            else:
                print(f"  ⚠️  Workflow API returned: HTTP {response.status_code}")
                print(f"     This might be expected if Duo features aren't fully enabled")
                return True  # Don't fail the test for this
                
        except Exception as e:
            print(f"  ⚠️  API test error: {e}")
            print("     This might be expected in development environment")
            return True  # Don't fail the test for this
    
    def run_comprehensive_test(self) -> bool:
        """Run all tests and return overall success."""
        print("🚀 Context 2.0 DAP Integration Test")
        print("===================================\n")
        
        tests = [
            ("GDK Services", self.test_gdk_services),
            ("GitLab Connection", self.test_gitlab_connection),
            ("Duo Test Project", lambda: self.find_duo_test_project() is not None),
            ("Duo Workflow Service", self.test_********************),
            ("Context 2.0 Files", self.test_context_2_files),
            ("Workflow Integration", self.test_workflow_integration),
            ("API Workflow Trigger", self.test_api_workflow_trigger),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    failed += 1
            except Exception as e:
                print(f"  ❌ {test_name}: Exception - {e}")
                failed += 1
            print()
        
        print(f"📊 Test Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All tests PASSED!")
            print("\n🔥 Context 2.0 DAP Integration is READY!")
            print("\n📋 You can now test Context 2.0 multi-agent system:")
            print("   • Via VS Code with GitLab Workflow extension")
            print("   • Via API calls to trigger workflows")
            print("   • Monitor logs with: gdk tail duo-workflow-service")
            return True
        else:
            print("❌ Some tests failed. Please review and fix issues.")
            print("\n🔧 Common fixes:")
            print("   • Run: ./setup_context_2_dap.sh")
            print("   • Check: gdk status")
            print("   • Restart: gdk restart duo-workflow-service rails")
            return False


def main():
    """Main test function."""
    # Check if API token is provided
    api_token = os.environ.get("GITLAB_API_TOKEN")
    if not api_token:
        print("💡 Tip: Set GITLAB_API_TOKEN environment variable for full API testing")
    
    # Run tests
    tester = Context2DAPTester(api_token=api_token)
    success = tester.run_comprehensive_test()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
