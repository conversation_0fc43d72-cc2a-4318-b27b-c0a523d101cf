# Critical Issue Summary - Context 2.0 Tool Parameter Fixes

## Executive Summary

I've identified and documented a **CRITICAL ARCHITECTURAL ISSUE** that prevents Context 2.0 specialist agents from functioning: **All GitLab and Git tools require project context (project_id, group_id, repository_url) that is NOT available to the agents.**

## Current Status

### ✅ Completed Fixes

1. **base_specialist_agent.py** - Fixed `_execute_tool()` method to pass dict directly to `ainvoke()`
2. **enhanced_state.py** - Fixed knowledge graph type handling (dict vs KnowledgeGraph object)
3. **repository_explorer.py** - Fixed 5 tool calls with correct parameter names
4. **code_navigator.py** - Fixed 3 tool calls with correct parameter names
5. **gitlab_ecosystem.py** - Commented out ALL tool calls that require missing context, added detailed TODOs

### ❌ Critical Issues Remaining

1. **Service Not Reloaded**: The duo-workflow-service is still running OLD code (error at line 313 shows old code)
2. **git_history.py**: Needs same treatment as gitlab_ecosystem.py
3. **Missing Project Context**: Fundamental architectural issue - agents can't call tools without project context

## The Core Problem

### What's Missing

The `AgentInvestigationContext` only contains:
```python
class AgentInvestigationContext(TypedDict):
    goal: str
    current_findings: Dict[str, Any]
    calling_agent: Optional[str]
    investigation_depth: int
    max_depth: int
    call_stack: List[str]
    # ❌ NO project_id
    # ❌ NO group_id
    # ❌ NO repository_url
    # ❌ NO previous_session_id
```

### What Tools Need

**GitLab Issue Tools:**
- `gitlab_issue_search`: Requires `id` (project/group ID), `search`, `search_type`
- `list_issues`: Requires `project_id` or `url`
- `get_issue`: Requires `project_id` + `issue_iid`
- `list_issue_notes`: Requires `project_id` + `issue_iid`

**GitLab Merge Request Tools:**
- `get_merge_request`: Requires `project_id` + `merge_request_iid`
- `gitlab_merge_request_search`: **DOESN'T EXIST** - needs to be implemented

**GitLab Project Tools:**
- `get_project`: Requires `project_id`

**GitLab Epic Tools:**
- `list_epics`: Requires `group_id` or `url`
- `get_epic`: Requires `group_id` + `epic_id`

**Git Commit Tools:**
- `list_commits`: Requires `project_id` or `url`
- `get_commit`: Requires `project_id` + `commit_sha`
- `get_commit_diff`: Requires `project_id` + `commit_sha`

**Git Command Tools:**
- `run_read_only_git_command`: Requires `repository_url`, `command`

**Session Tools:**
- `get_previous_session_context`: Requires `previous_session_id`

## Files Modified

### gitlab-ai-gateway/********************/agents/context_2_0/gitlab_ecosystem.py

**Changes:**
- Line 181: `get_project` - Commented out, added TODO
- Lines 275-295: `gitlab_issue_search` + `list_issues` - Commented out, added TODOs
- Lines 317-337: `get_issue` + `list_issue_notes` - Commented out, added TODOs
- Lines 438-468: `gitlab_merge_request_search` + `get_merge_request` - Commented out, added TODOs + note that search tool doesn't exist
- Line 560-571: `list_epics` - Commented out, added TODO
- Line 607-623: `get_previous_session_context` - Commented out, added TODO

All commented-out code includes:
- Detailed explanation of what parameters are required
- Correct parameter names (fixed from incorrect ones)
- Stub return values to prevent crashes: `{"success": False, "content": None}`

**Syntax Check:** ✅ PASSED

## Recommended Solutions

### Option 1: Add Project Context to AgentInvestigationContext (RECOMMENDED)

```python
class AgentInvestigationContext(TypedDict):
    goal: str
    current_findings: Dict[str, Any]
    calling_agent: Optional[str]
    investigation_depth: int
    max_depth: int
    call_stack: List[str]
    # NEW FIELDS:
    project_id: Optional[str]  # GitLab project ID
    group_id: Optional[str]    # GitLab group ID
    repository_url: Optional[str]  # Git repository URL
    previous_session_id: Optional[int]  # For historical context
```

**Pros:**
- Clean, explicit context passing
- Easy to understand and maintain
- Agents have direct access to required context

**Cons:**
- Requires changes to all agent invocations
- Need to extract context from workflow state

### Option 2: Extract Context from Workflow State

Modify specialist agents to extract project context from the workflow state before calling tools.

**Pros:**
- No changes to AgentInvestigationContext
- Context stays in workflow state

**Cons:**
- More complex - agents need to know how to extract context
- Tighter coupling between agents and workflow state structure

### Option 3: Use Default Project from Workflow Configuration

Configure a default project_id at the workflow level that all agents can access via self.config or similar.

**Pros:**
- Simple implementation
- No changes to context passing

**Cons:**
- Less flexible - assumes single project per workflow
- Doesn't solve group_id, repository_url, previous_session_id issues

## Immediate Next Steps

### 1. Restart duo-workflow-service ⚠️ CRITICAL

The service is still running old code. You need to restart it to load the fixes:

```bash
# In your GDK directory
gdk restart gitlab-ai-gateway
# OR
gdk stop gitlab-ai-gateway && gdk start gitlab-ai-gateway
```

### 2. Implement Project Context Solution

Choose one of the recommended solutions above and implement it. I recommend **Option 1** as it's the cleanest approach.

### 3. Fix git_history.py

Apply the same treatment as gitlab_ecosystem.py:
- Comment out all tool calls that require missing context
- Add detailed TODOs with correct parameter names
- Add stub return values

### 4. Implement gitlab_merge_request_search Tool

This tool is referenced but doesn't exist. Either:
- Implement it in `********************/tools/search.py`
- Remove references to it from gitlab_ecosystem.py

### 5. Test End-to-End

After implementing project context solution:
1. Restart duo-workflow-service
2. Test in local GitLab instance Flow UI
3. Verify LangSmith traces show successful tool execution
4. Confirm workflow completes successfully

## Documentation Created

1. **TOOL_PARAMETER_FIXES_SUMMARY.md** - Detailed fixes and tool parameter reference
2. **CRITICAL_ISSUE_SUMMARY.md** (this file) - Executive summary and recommendations

## Key Takeaway

**The Context 2.0 specialist agents cannot function until project context is made available to them.** All the tool parameter fixes are correct, but the tools can't be called without the required context parameters.

This is a fundamental architectural issue that must be resolved before the Context 2.0 workflow can work properly.

