#!/usr/bin/env python3
"""
Integration test for Context 2.0 LLM-driven agents.

This script tests that all Context 2.0 agents can properly use LLM-driven investigation
and fall back to heuristics when LLM is unavailable.
"""

import asyncio
import sys
from pathlib import Path
from typing import Any, Dict

# Add gitlab-ai-gateway to path
sys.path.insert(0, str(Path(__file__).parent / "gitlab-ai-gateway"))

from ********************.agents.context_2_0.base_specialist_agent import (
    AgentInvestigationContext,
)
from ********************.agents.context_2_0.repository_explorer import (
    RepositoryExplorerAgent,
)
from ********************.agents.context_2_0.code_navigator import Code<PERSON>avigator<PERSON>gent
from ********************.agents.context_2_0.git_history import GitHistoryAgent
from ********************.agents.context_2_0.gitlab_ecosystem import (
    GitLabEcosystemAgent,
)
from ********************.agents.context_2_0.context_synthesizer import (
    ContextSynthesizerAgent,
)
from ********************.agents.context_2_0.orchestrator import Orchestrator<PERSON>gent
from ********************.agents.context_2_0.agent_tool import AgentToolRegistry
from ********************.agents.context_2_0.tool_distribution_integration import (
    Context2ToolsetFactory,
)
from ********************.components.tools_registry import ToolsRegistry
from ********************.tools.toolset import Toolset
from lib.internal_events.event_enum import CategoryEnum


class Context2LLMIntegrationTest:
    """Test suite for Context 2.0 LLM integration."""

    def __init__(self):
        self.workflow_id = "test-workflow-123"
        self.workflow_type = CategoryEnum.WORKFLOW_SOFTWARE_DEVELOPMENT_2_0
        self.results = []

    def log_test(self, test_name: str, passed: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        self.results.append((test_name, passed))
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")

    def test_prompt_files_exist(self) -> bool:
        """Test that all prompt definition files exist."""
        print("\n📂 Testing Prompt Definition Files...")

        base_path = Path("gitlab-ai-gateway/ai_gateway/prompts/definitions/workflow")
        agents = [
            "context_2_0_orchestrator",
            "context_2_0_repository_explorer",
            "context_2_0_code_navigator",
            "context_2_0_git_history",
            "context_2_0_gitlab_ecosystem",
            "context_2_0_context_synthesizer",
        ]

        all_exist = True
        for agent in agents:
            agent_path = base_path / agent
            base_yml = agent_path / "base" / "1.0.0.yml"
            system_jinja = agent_path / "system" / "1.0.0.jinja"

            if base_yml.exists() and system_jinja.exists():
                self.log_test(f"Prompt files for {agent}", True)
            else:
                self.log_test(
                    f"Prompt files for {agent}",
                    False,
                    f"Missing: {base_yml if not base_yml.exists() else system_jinja}",
                )
                all_exist = False

        return all_exist

    def test_agent_has_llm_infrastructure(self) -> bool:
        """Test that agents have LLM infrastructure parameters."""
        print("\n🔧 Testing Agent LLM Infrastructure...")

        # Create a mock toolset
        tools_registry = ToolsRegistry(
            project={},
            workflow_id=self.workflow_id,
            workflow_type=self.workflow_type,
            http_client=None,
        )
        toolset = Toolset(tools={})

        try:
            # Test Repository Explorer
            agent = RepositoryExplorerAgent(
                workflow_id=self.workflow_id,
                workflow_type=self.workflow_type,
                toolset=toolset,
                tools_registry=tools_registry,
                prompt_registry=None,  # Test without LLM
                user=None,
                http_client=None,
            )

            # Check that agent has the required attributes
            has_prompt_registry = hasattr(agent, "prompt_registry")
            has_user = hasattr(agent, "user")
            has_http_client = hasattr(agent, "http_client")
            has_get_prompt_name = hasattr(agent, "_get_prompt_name")

            if all([has_prompt_registry, has_user, has_http_client, has_get_prompt_name]):
                self.log_test("Repository Explorer has LLM infrastructure", True)
                
                # Test _get_prompt_name returns correct value
                prompt_name = agent._get_prompt_name()
                if prompt_name == "workflow/context_2_0_repository_explorer":
                    self.log_test("Repository Explorer prompt name correct", True)
                else:
                    self.log_test(
                        "Repository Explorer prompt name correct",
                        False,
                        f"Expected 'workflow/context_2_0_repository_explorer', got '{prompt_name}'",
                    )
                    return False
            else:
                self.log_test(
                    "Repository Explorer has LLM infrastructure",
                    False,
                    f"Missing: prompt_registry={has_prompt_registry}, user={has_user}, "
                    f"http_client={has_http_client}, _get_prompt_name={has_get_prompt_name}",
                )
                return False

            return True

        except Exception as e:
            self.log_test("Agent LLM infrastructure", False, f"Error: {e}")
            return False

    async def test_agent_heuristic_fallback(self) -> bool:
        """Test that agents fall back to heuristics when LLM unavailable."""
        print("\n🔄 Testing Heuristic Fallback...")

        tools_registry = ToolsRegistry(
            project={},
            workflow_id=self.workflow_id,
            workflow_type=self.workflow_type,
            http_client=None,
        )
        toolset = Toolset(tools={})

        try:
            # Create agent without LLM infrastructure
            agent = RepositoryExplorerAgent(
                workflow_id=self.workflow_id,
                workflow_type=self.workflow_type,
                toolset=toolset,
                tools_registry=tools_registry,
                prompt_registry=None,  # No LLM
                user=None,
                http_client=None,
            )

            # Create investigation context
            context: AgentInvestigationContext = {
                "goal": "Test heuristic fallback",
                "current_findings": {},
                "calling_agent": None,
                "investigation_depth": 0,
                "max_depth": 2,
                "call_stack": [],
            }

            # Test that investigate method works (should fall back to heuristics)
            report = await agent.investigate("Analyze project structure", context)

            if report and report.agent_name == "Repository Explorer":
                self.log_test("Heuristic fallback works", True, "Agent completed investigation without LLM")
                return True
            else:
                self.log_test("Heuristic fallback works", False, "Investigation failed")
                return False

        except Exception as e:
            self.log_test("Heuristic fallback", False, f"Error: {e}")
            return False

    def test_all_agents_have_prompt_names(self) -> bool:
        """Test that all specialist agents have correct prompt names."""
        print("\n📝 Testing Agent Prompt Names...")

        tools_registry = ToolsRegistry(
            project={},
            workflow_id=self.workflow_id,
            workflow_type=self.workflow_type,
            http_client=None,
        )
        toolset = Toolset(tools={})

        agents_and_prompts = [
            (RepositoryExplorerAgent, "workflow/context_2_0_repository_explorer"),
            (CodeNavigatorAgent, "workflow/context_2_0_code_navigator"),
            (GitHistoryAgent, "workflow/context_2_0_git_history"),
            (GitLabEcosystemAgent, "workflow/context_2_0_gitlab_ecosystem"),
            (ContextSynthesizerAgent, "workflow/context_2_0_context_synthesizer"),
        ]

        all_correct = True
        for agent_class, expected_prompt in agents_and_prompts:
            try:
                agent = agent_class(
                    workflow_id=self.workflow_id,
                    workflow_type=self.workflow_type,
                    toolset=toolset,
                    tools_registry=tools_registry,
                )

                prompt_name = agent._get_prompt_name()
                if prompt_name == expected_prompt:
                    self.log_test(f"{agent_class.__name__} prompt name", True)
                else:
                    self.log_test(
                        f"{agent_class.__name__} prompt name",
                        False,
                        f"Expected '{expected_prompt}', got '{prompt_name}'",
                    )
                    all_correct = False

            except Exception as e:
                self.log_test(f"{agent_class.__name__} prompt name", False, f"Error: {e}")
                all_correct = False

        return all_correct

    def test_orchestrator_llm_ready(self) -> bool:
        """Test that orchestrator is LLM-ready."""
        print("\n🎯 Testing Orchestrator LLM Infrastructure...")

        try:
            from ********************.llm_factory import AnthropicConfig

            model_config = AnthropicConfig(
                model_name="claude-3-5-sonnet-20241022",
                max_tokens=4096,
            )

            agent_registry = AgentToolRegistry()

            orchestrator = OrchestratorAgent(
                workflow_id=self.workflow_id,
                workflow_type=self.workflow_type,
                model_config=model_config,
                agent_registry=agent_registry,
                prompt_registry=None,  # Test without LLM
                user=None,
                http_client=None,
            )

            # Check orchestrator has LLM infrastructure
            has_prompt_registry = hasattr(orchestrator, "prompt_registry")
            has_user = hasattr(orchestrator, "user")
            has_http_client = hasattr(orchestrator, "http_client")

            if all([has_prompt_registry, has_user, has_http_client]):
                self.log_test("Orchestrator has LLM infrastructure", True)
                return True
            else:
                self.log_test(
                    "Orchestrator has LLM infrastructure",
                    False,
                    f"Missing attributes",
                )
                return False

        except Exception as e:
            self.log_test("Orchestrator LLM infrastructure", False, f"Error: {e}")
            return False

    async def run_all_tests(self) -> bool:
        """Run all integration tests."""
        print("🚀 Context 2.0 LLM Integration Test Suite")
        print("=" * 50)

        # Run tests
        test_results = [
            self.test_prompt_files_exist(),
            self.test_agent_has_llm_infrastructure(),
            await self.test_agent_heuristic_fallback(),
            self.test_all_agents_have_prompt_names(),
            self.test_orchestrator_llm_ready(),
        ]

        # Print summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print("=" * 50)

        passed = sum(1 for _, result in self.results if result)
        total = len(self.results)

        for test_name, result in self.results:
            status = "✅" if result else "❌"
            print(f"{status} {test_name}")

        print(f"\n{'✅' if all(test_results) else '❌'} Overall: {passed}/{total} tests passed")

        return all(test_results)


async def main():
    """Main entry point."""
    tester = Context2LLMIntegrationTest()
    success = await tester.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())

