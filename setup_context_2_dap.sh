#!/bin/bash
# Setup Context 2.0 DAP for Local Testing
# This script configures your local GitLab Development Kit to test Context 2.0 multi-agent system

set -e

echo "🚀 Setting up Context 2.0 DAP for Local Testing"
echo "================================================"

# Check if we're in the right directory
if [ ! -d "gitlab-ai-gateway" ]; then
    echo "❌ Error: Please run this script from your GDK root directory"
    echo "   Expected to find gitlab-ai-gateway directory"
    exit 1
fi

# Check if GDK is installed
if ! command -v gdk &> /dev/null; then
    echo "❌ Error: GDK command not found. Please install GitLab Development Kit first"
    echo "   See: https://gitlab.com/gitlab-org/gitlab-development-kit"
    exit 1
fi

echo "✅ GDK found"

# Step 1: Enable AI Gateway and Duo Workflow
echo ""
echo "📋 Step 1: Configuring GDK for Duo Agent Platform..."

# Enable AI Gateway
echo "  🔧 Enabling AI Gateway..."
gdk config set gitlab_ai_gateway.enabled true

# Enable Duo Workflow Service
echo "  🔧 Enabling Duo Workflow Service..."
gdk config set duo_workflow.enabled true

# Enable LLM cache for faster development
echo "  🔧 Enabling LLM cache for faster iteration..."
gdk config set duo_workflow.llm_cache true

# Enable debug mode
echo "  🔧 Enabling debug mode..."
gdk config set duo_workflow.debug true

echo "  ✅ GDK configuration updated"

# Step 2: Reconfigure GDK
echo ""
echo "📋 Step 2: Reconfiguring GDK..."
gdk reconfigure

echo "  ✅ GDK reconfigured"

# Step 3: Validate Context 2.0 files
echo ""
echo "📋 Step 3: Validating Context 2.0 implementation..."

cd gitlab-ai-gateway/********************/agents/context_2_0

# Check syntax of key files
echo "  🔍 Checking Python syntax..."
python3 -m py_compile context_2_workflow.py && echo "    ✅ context_2_workflow.py"
python3 -m py_compile tool_distribution_system.py && echo "    ✅ tool_distribution_system.py"
python3 -m py_compile tool_distribution_integration.py && echo "    ✅ tool_distribution_integration.py"

# Run validation tests
echo "  🧪 Running Context 2.0 validation tests..."
if python3 validate_tool_distribution.py > /dev/null 2>&1; then
    echo "    ✅ Tool distribution validation passed"
else
    echo "    ⚠️  Tool distribution validation had warnings (this is OK for basic setup)"
fi

cd ../../../../

echo "  ✅ Context 2.0 validation complete"

# Step 4: Setup GitLab project for testing
echo ""
echo "📋 Step 4: Setting up GitLab project for testing..."

cd gitlab

# Check if we need to run the setup rake task
if ! bundle exec rails runner "puts Group.find_by(path: 'gitlab-duo')&.name || 'Not found'" 2>/dev/null | grep -q "GitLab Duo"; then
    echo "  🔧 Creating Ultimate group and project with Duo features..."
    GITLAB_SIMULATE_SAAS=1 bundle exec rake 'gitlab:duo:setup'
    echo "    ✅ GitLab Duo test project created"
else
    echo "    ✅ GitLab Duo test project already exists"
fi

cd ..

# Step 5: Start services
echo ""
echo "📋 Step 5: Starting services..."

echo "  🔄 Restarting Duo Workflow Service and Rails..."
gdk restart duo-workflow-service rails

echo "  ✅ Services restarted"

# Step 6: Verify setup
echo ""
echo "📋 Step 6: Verifying setup..."

# Check if services are running
sleep 5

if gdk status | grep -q "duo-workflow-service.*up"; then
    echo "  ✅ Duo Workflow Service is running"
else
    echo "  ⚠️  Duo Workflow Service may not be running properly"
    echo "     Check with: gdk status"
fi

if gdk status | grep -q "rails.*up"; then
    echo "  ✅ Rails is running"
else
    echo "  ⚠️  Rails may not be running properly"
    echo "     Check with: gdk status"
fi

# Step 7: Display testing instructions
echo ""
echo "🎉 Context 2.0 DAP Setup Complete!"
echo "=================================="
echo ""
echo "📋 Next Steps for Testing:"
echo ""
echo "1. 🌐 Open your GitLab instance:"
echo "   http://localhost:3000"
echo ""
echo "2. 📁 Navigate to the test project:"
echo "   http://localhost:3000/gitlab-duo/test"
echo ""
echo "3. ⚙️  Enable flow execution in project settings:"
echo "   Settings > General > GitLab Duo > Allow flow execution"
echo ""
echo "4. 🔧 Test via VS Code (IDE Flows):"
echo "   - Clone the project: git clone http://localhost:3000/gitlab-duo/test.git"
echo "   - Open in VS Code"
echo "   - Install GitLab Workflow extension"
echo "   - Authenticate with your local GDK"
echo "   - Try Duo Chat or Flows"
echo ""
echo "5. 🧪 Test via API (Remote Flows):"
echo "   - Get your project ID from the project page"
echo "   - Get an API token: User Settings > Access Tokens"
echo "   - Test with curl (see documentation)"
echo ""
echo "📊 Monitor Context 2.0 Multi-Agent System:"
echo "   - Check logs: gdk tail duo-workflow-service"
echo "   - View LangSmith traces (if configured)"
echo "   - Monitor agent specialization and tool distribution"
echo ""
echo "🔥 Your Context 2.0 system is ready to transform DAP into an absolute beast!"
echo ""
echo "📚 For more details, see: doc/howto/duo_agent_platform.md"
