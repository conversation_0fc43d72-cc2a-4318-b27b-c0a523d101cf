#!/usr/bin/env python3
"""
Test script to verify our CategoryEnum and string_to_category_enum changes are working correctly.
"""

import sys
import os

# Add the gitlab-ai-gateway directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'gitlab-ai-gateway'))

def test_category_enum():
    """Test that CategoryEnum has the new software_development_2_0 value."""
    try:
        from lib.internal_events.event_enum import CategoryEnum
        
        print("✅ CategoryEnum imported successfully")
        print(f"Available CategoryEnum values: {[e.value for e in CategoryEnum]}")
        
        # Check if our new enum value exists
        if hasattr(CategoryEnum, 'WORKFLOW_SOFTWARE_DEVELOPMENT_2_0'):
            print("✅ WORKFLOW_SOFTWARE_DEVELOPMENT_2_0 enum value exists")
            print(f"   Value: {CategoryEnum.WORKFLOW_SOFTWARE_DEVELOPMENT_2_0}")
        else:
            print("❌ WORKFLOW_SOFTWARE_DEVELOPMENT_2_0 enum value NOT found")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing CategoryEnum: {e}")
        return False

def test_string_to_category_enum():
    """Test the string_to_category_enum function with our changes."""
    try:
        # This is more complex due to dependencies, so we'll just check the file content
        server_file = os.path.join(os.path.dirname(__file__), 'gitlab-ai-gateway', '********************', 'server.py')
        
        with open(server_file, 'r') as f:
            content = f.read()
            
        # Check if our changes are in the file
        if 'if category_string is None:' in content:
            print("✅ string_to_category_enum function has None check")
        else:
            print("❌ string_to_category_enum function missing None check")
            return False
            
        if 'CategoryEnum.WORKFLOW_SOFTWARE_DEVELOPMENT_2_0' in content:
            print("✅ string_to_category_enum function returns WORKFLOW_SOFTWARE_DEVELOPMENT_2_0")
        else:
            print("❌ string_to_category_enum function does not return WORKFLOW_SOFTWARE_DEVELOPMENT_2_0")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing string_to_category_enum: {e}")
        return False

def test_workflow_registry():
    """Test that the workflow registry has our changes."""
    try:
        registry_file = os.path.join(os.path.dirname(__file__), 'gitlab-ai-gateway', '********************', 'workflows', 'registry.py')
        
        with open(registry_file, 'r') as f:
            content = f.read()
            
        # Check if our changes are in the file
        if 'software_development_2_0.Workflow' in content:
            print("✅ Workflow registry imports and uses software_development_2_0.Workflow")
        else:
            print("❌ Workflow registry missing software_development_2_0.Workflow")
            return False
            
        if 'Context 2.0 multi-agent workflow' in content:
            print("✅ Workflow registry has Context 2.0 comment")
        else:
            print("❌ Workflow registry missing Context 2.0 comment")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing workflow registry: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing CategoryEnum and workflow changes...\n")
    
    tests = [
        ("CategoryEnum changes", test_category_enum),
        ("string_to_category_enum changes", test_string_to_category_enum),
        ("Workflow registry changes", test_workflow_registry),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"Testing {test_name}:")
        result = test_func()
        results.append(result)
        print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The changes should work correctly.")
        print("\n🔍 Expected behavior:")
        print("   - When Flow mode is used (no workflow_definition)")
        print("   - LangSmith traces should show: workflow_type: software_development_2_0")
        print("   - Context 2.0 multi-agent workflow should be used")
    else:
        print("❌ Some tests failed. Please check the changes.")
        
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
