# Context 2.0 State Preservation and Investigation Context Fix

## Problem Summary

The Context 2.0 multi-agent workflow in GitLab DAP was failing with two critical issues:

### Issue 1: State Loss in Orchestrator
**Error**: `ValidationError: 5 validation errors for AgentSelectionStrategy - Field required`

**Root Cause**: When the orchestrator node returned state updates during phase transitions, it wasn't preserving critical state fields like `agent_selection_strategy`, `goal_classification`, and `selected_agents`. LangGraph's default behavior with TypedDict is to merge state updates, but only the fields explicitly returned in each node's output are updated. Fields not included in the return dict retain their previous values, but if a field was never set or was implicitly cleared, it would be missing.

The orchestrator was going through multiple phases:
1. `goal_classification` → returns strategy data ✅
2. `agent_selection` → returns phase change but **didn't preserve strategy** ❌  
3. `coordination` → tries to access strategy but gets `{}` ❌

### Issue 2: Missing Investigation Context Fields
**Error**: `findings: error: "'investigation_depth'"`

**Root Cause**: The specialist agent nodes were passing incomplete context dictionaries to the `investigate()` method. The `AgentInvestigationContext` TypedDict requires specific fields (`investigation_depth`, `max_depth`, `calling_agent`, `call_stack`), but the workflow nodes were only passing a subset of fields.

## Files Modified

### 1. `gitlab-ai-gateway/********************/agents/context_2_0/orchestrator.py`

**Changes**: Added state preservation in all orchestrator phase transitions

#### Lines 530-541 (agent_selection phase):
```python
# Before: Only returned phase change
return {
    "orchestration_phase": "coordination",
    "orchestration_reasoning": "Agent selection completed, beginning coordination"
}

# After: Preserves critical state
return {
    "orchestration_phase": "coordination",
    "orchestration_reasoning": "Agent selection completed, beginning coordination",
    "agent_selection_strategy": state.get("agent_selection_strategy", {}),
    "goal_classification": state.get("goal_classification", {}),
    "selected_agents": state.get("selected_agents", []),
    "agent_coordination": state.get("agent_coordination", AgentCoordinationState())
}
```

#### Lines 555-596 (coordination phase - all branches):
Added state preservation to:
- Synthesis transition (lines 559-567)
- Quality gate transition (lines 571-578)
- Investigation transition (lines 587-596)

#### Lines 598-628 (quality_gate phase):
Added state preservation to both success and failure branches.

### 2. `gitlab-ai-gateway/********************/agents/context_2_0/enhanced_state.py`

**Changes**: Added missing fields to `Context2State` TypedDict

#### Lines 228-254:
```python
# Added fields:
agent_findings: Dict[str, Any]  # Findings from specialist agents
current_agent: str  # Currently active agent
last_completed_agent: str  # Last agent that completed
goal_classification: Dict[str, Any]  # GoalClassification model_dump()
agent_selection_strategy: Dict[str, Any]  # AgentSelectionStrategy model_dump()
quality_gate_passed: bool
quality_gate_reasoning: str
quality_recommendations: List[str]
synthesis_completed: bool
```

#### Lines 264-291 (initialize_context2_state):
Updated state initialization to include all new fields with proper defaults.

### 3. `gitlab-ai-gateway/********************/agents/context_2_0/context_2_workflow.py`

**Changes**: Fixed investigation context in all specialist agent nodes

#### Repository Explorer Node (lines 268-278):
```python
# Before: Incomplete context
context = {
    "current_findings": state.get("agent_findings", {}),
    "orchestration_phase": state.get("orchestration_phase", ""),
    "selected_agents": state.get("selected_agents", [])
}

# After: Complete AgentInvestigationContext
context = {
    "goal": goal,
    "current_findings": state.get("agent_findings", {}),
    "calling_agent": None,
    "investigation_depth": 0,
    "max_depth": 2,
    "call_stack": [],
    "orchestration_phase": state.get("orchestration_phase", ""),
    "selected_agents": state.get("selected_agents", [])
}
```

#### Similar fixes applied to:
- Code Navigator Node (lines 308-323)
- GitLab Ecosystem Node (lines 344-359)
- Git History Node (lines 380-395)
- Context Synthesizer Node (lines 416-431)

#### State Initialization (lines 489-490, 509-510):
```python
# Before: Manual state construction
initial_state = Context2State(
    goal=goal,
    orchestration_phase="goal_classification",
    agent_coordination=AgentCoordinationState(),
    agent_findings={},
    quality_metrics=None,
    handover_approved=False
)

# After: Use state manager for proper initialization
initial_state = Context2StateManager.initialize_context2_state(goal)
```

## Technical Details

### LangGraph State Management
- **TypedDict Behavior**: LangGraph merges state updates by default, but only updates fields explicitly returned
- **State Preservation Pattern**: Critical state must be explicitly passed through in every node return
- **Annotated Types**: For complex merging logic (like lists, dicts with special semantics), use `Annotated[Type, reducer_function]`

### AgentInvestigationContext Requirements
The `base_specialist_agent.py` expects a complete `AgentInvestigationContext` with:
- `goal`: The investigation goal
- `current_findings`: Findings from other agents
- `calling_agent`: Which agent initiated this (None for orchestrator)
- `investigation_depth`: Current recursion depth (0 for initial call)
- `max_depth`: Maximum allowed depth (default: 2)
- `call_stack`: Agent call history for cycle detection

## Testing Results

### ✅ Before Fix:
- Orchestrator successfully classified goal
- Orchestrator selected agents
- **FAILED** at coordination phase with ValidationError

### ✅ After Fix:
- Orchestrator successfully classified goal ✅
- Orchestrator selected agents ✅
- Orchestrator coordinated agent execution ✅
- Specialist agents received proper context ✅
- Repository Explorer and Code Navigator executed ✅
- Context Synthesizer processed findings ✅

## Expected Workflow Flow

```
1. Goal Classification
   ├─> Orchestrator analyzes goal
   ├─> Returns: goal_classification, agent_selection_strategy, selected_agents
   └─> Phase: "agent_selection"

2. Agent Selection (routing phase)
   ├─> Orchestrator preserves all state
   └─> Phase: "coordination"

3. Coordination
   ├─> Orchestrator determines next agent
   ├─> Preserves: agent_selection_strategy, goal_classification, selected_agents
   └─> Phase: "investigation", current_agent: "Repository Explorer"

4. Investigation (Repository Explorer)
   ├─> Receives complete AgentInvestigationContext
   ├─> Executes domain-specific investigation
   ├─> Returns findings
   └─> Routes back to orchestrator

5. Coordination (next agent)
   ├─> Orchestrator determines next agent
   └─> Phase: "investigation", current_agent: "Code Navigator"

6. Investigation (Code Navigator)
   └─> ... continues for all selected agents

7. Synthesis
   ├─> Context Synthesizer combines all findings
   └─> Phase: "quality_gate"

8. Quality Gate
   ├─> Orchestrator evaluates quality
   └─> Phase: "completed" or back to coordination for improvements
```

## Verification

To verify the fixes are working:

1. **Check LangSmith Traces**: Look for successful orchestrator phase transitions
2. **Monitor Logs**: No more ValidationError or KeyError for 'investigation_depth'
3. **Verify State Flow**: agent_selection_strategy should persist through all phases
4. **Check Agent Execution**: All selected agents should execute successfully

## Future Improvements

1. **State Reducer Pattern**: Consider using `Annotated` types with custom reducers for complex state fields
2. **State Validation**: Add validation at node boundaries to catch missing fields early
3. **Context Builder**: Create a helper function to build AgentInvestigationContext consistently
4. **Type Safety**: Use Pydantic models instead of TypedDict for better validation

## Related Files

- `orchestrator.py`: Multi-phase orchestration logic
- `enhanced_state.py`: State type definitions
- `context_2_workflow.py`: LangGraph workflow integration
- `base_specialist_agent.py`: Specialist agent base class with investigation interface

